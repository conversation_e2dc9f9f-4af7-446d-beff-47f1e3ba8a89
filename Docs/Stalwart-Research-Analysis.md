# Stalwart Mail Server - Research Analysis

## Overview
This document provides a comprehensive analysis of Stalwart Mail Server's authentication system, enterprise features, and spam filtering capabilities. The research focuses on understanding the implementation details and potential for building LLM-enhanced email features.

## 1. Authentication Implementation (`/crates/common/src/auth`)

### Core Authentication Architecture

The authentication system is built around several key components:

#### 1.1 AccessToken Structure
```rust
pub struct AccessToken {
    pub primary_id: u32,
    pub member_of: Vec<u32>,
    pub access_to: VecMap<u32, Bitmap<Collection>>,
    pub name: String,
    pub description: Option<String>,
    pub locale: Option<String>,
    pub emails: Vec<String>,
    pub quota: u64,
    pub permissions: Permissions,
    pub tenant: Option<TenantInfo>,
    pub concurrent_http_requests: Option<ConcurrencyLimiter>,
    pub concurrent_imap_requests: Option<ConcurrencyLimiter>,
    pub concurrent_uploads: Option<ConcurrencyLimiter>,
    pub revision: u64,
    pub obj_size: u64,
}
```

#### 1.2 Authentication Flow
1. **Credential Validation**: Supports multiple authentication methods:
   - Plain text credentials
   - OAuth Bearer tokens
   - SASL mechanisms (PLAIN, OAUTHBEARER, XOAUTH2)
   
2. **Directory Integration**: Queries external directories for user validation
3. **Fallback Authentication**: Supports fallback admin and master user accounts
4. **Permission Checking**: Validates user permissions for specific operations

#### 1.3 Key Features
- **Multi-tenant Support**: Built-in tenant isolation
- **Role-based Access Control**: Granular permission system
- **Concurrency Limiting**: Per-user connection limits
- **Token Caching**: Efficient access token management with revision tracking
- **OAuth Integration**: Full OAuth 2.0 support with token introspection

### 1.4 SASL Implementation
- Supports PLAIN and OAuth Bearer authentication
- Secure challenge-response mechanisms
- Proper encoding/decoding of authentication data

## 2. Enterprise Features (`/crates/common/src/enterprise`)

### 2.1 LLM Integration (`llm.rs`)

#### API Configuration
```rust
pub struct AiApiConfig {
    pub id: String,
    pub api_type: ApiType,  // ChatCompletion or TextCompletion
    pub url: String,
    pub model: String,
    pub timeout: Duration,
    pub headers: HeaderMap,
    pub tls_allow_invalid_certs: bool,
    pub default_temperature: f64,
}
```

#### Supported API Types
- **Chat Completion**: OpenAI-style chat APIs
- **Text Completion**: Traditional completion APIs
- **Configurable Models**: Support for multiple AI providers
- **Custom Headers**: Authentication and custom header support

#### LLM Spam Filtering
```rust
pub struct SpamFilterLlmConfig {
    pub model: Arc<AiApiConfig>,
    pub temperature: f64,
    pub prompt: String,
    pub separator: char,
    pub index_category: usize,
    pub index_confidence: Option<usize>,
    pub index_explanation: Option<usize>,
    pub categories: AHashSet<String>,
    pub confidence: AHashSet<String>,
}
```

**Key Features:**
- Configurable prompts for spam classification
- Structured response parsing (category, confidence, explanation)
- Integration with existing spam filter pipeline
- Performance monitoring and logging

### 2.2 Alerts System (`alerts.rs`)

#### Alert Configuration
```rust
pub struct MetricAlert {
    pub id: String,
    pub condition: Expression,
    pub method: Vec<AlertMethod>,
}

pub enum AlertMethod {
    Email {
        from_name: Option<String>,
        from_addr: String,
        to: Vec<String>,
        subject: AlertContent,
        body: AlertContent,
    },
    Event {
        message: Option<AlertContent>,
    },
}
```

**Features:**
- Expression-based alert conditions
- Multiple notification methods (email, events)
- Template-based alert content with metric interpolation
- Automatic email generation with proper headers

### 2.3 Undelete Functionality (`undelete.rs`)

#### Implementation
```rust
pub struct Undelete {
    pub retention: Duration,
}

pub struct DeletedBlob<H, T, C> {
    pub hash: H,
    pub size: usize,
    pub deleted_at: T,
    pub expires_at: T,
    pub collection: C,
}
```

**Features:**
- Configurable retention periods
- Blob-level recovery tracking
- Automatic cleanup of expired deleted items
- Collection-aware deletion tracking

### 2.4 Enterprise Licensing
- License validation with digital signatures
- Automatic license renewal
- Account limits enforcement
- Feature gating based on license status

## 3. Spam Filter Implementation (`/crates/spam-filter`)

### 3.1 Multi-layered Analysis Pipeline

The spam filter employs a comprehensive analysis pipeline:

1. **IP Address Analysis**: Reputation and geolocation checks
2. **Authentication Analysis**: DMARC/SPF/DKIM/ARC validation
3. **Header Analysis**: Generic and specific header validation
4. **Content Analysis**: Subject, body, and MIME analysis
5. **URL Analysis**: Link reputation and safety checks
6. **LLM Classification**: AI-powered content analysis (Enterprise)
7. **Bayes Classification**: Statistical spam detection
8. **Reputation Tracking**: Sender reputation management

### 3.2 Bayes Classifier
```rust
impl SpamFilterAnalyzeBayes for Server {
    async fn spam_filter_analyze_bayes_classify(&self, ctx: &mut SpamFilterContext<'_>) {
        // Statistical classification based on learned patterns
        // Configurable spam/ham thresholds
        // Integration with training data
    }
}
```

### 3.3 DNSBL Integration
- Multiple DNSBL provider support
- Configurable check limits per category
- Caching for performance optimization
- Support for IP, domain, email, and URL checks

### 3.4 Rule Engine
- Custom rule definitions for headers and body content
- Expression-based rule evaluation
- Location-aware rules (body text, HTML, attachments)
- Tag-based result classification

## 4. Sieve Implementation (`/crates/managesieve`)

### 4.1 ManageSieve Protocol Support

#### Core Operations
- **LISTSCRIPTS**: List available Sieve scripts
- **GETSCRIPT**: Retrieve script content
- **PUTSCRIPT**: Upload/update scripts
- **DELETESCRIPT**: Remove scripts
- **SETACTIVE**: Activate/deactivate scripts
- **CHECKSCRIPT**: Validate script syntax

#### Script Management
```rust
pub struct SieveScript {
    pub name: String,
    pub blob_hash: BlobHash,
    pub is_active: bool,
    pub size: u32,
}
```

### 4.2 JMAP Integration
- Full JMAP support for Sieve script management
- Query and filtering capabilities
- State synchronization
- Blob-based script storage

### 4.3 Security Features
- Permission-based access control
- Script validation before storage
- Active script protection (cannot delete active scripts)
- Tenant isolation

## 5. Building on These Features

### 5.1 LLM Enhancement Opportunities

#### Email Classification
- **Content Categorization**: Extend beyond spam to classify emails by topic, urgency, or department
- **Sentiment Analysis**: Analyze email tone for customer service prioritization
- **Language Detection**: Automatic language identification for multi-lingual organizations

#### Smart Filtering
- **Intent Recognition**: Understand email purpose (request, notification, marketing)
- **Priority Scoring**: AI-driven importance assessment
- **Auto-tagging**: Intelligent label assignment based on content

#### Response Assistance
- **Draft Suggestions**: AI-powered email composition assistance
- **Template Recommendations**: Context-aware template suggestions
- **Translation Services**: Automatic email translation

### 5.2 Alert System Extensions

#### Predictive Alerts
- **Anomaly Detection**: ML-based unusual pattern identification
- **Capacity Planning**: Predictive resource usage alerts
- **Security Monitoring**: Behavioral analysis for threat detection

#### Smart Notifications
- **Adaptive Thresholds**: Self-adjusting alert conditions
- **Escalation Policies**: Intelligent alert routing
- **Correlation Analysis**: Related event grouping

### 5.3 Undelete Enhancements

#### Intelligent Recovery
- **Content-based Search**: Find deleted emails by content similarity
- **Bulk Recovery**: Restore related emails automatically
- **Version History**: Track email modifications over time

## 6. Email Client Compatibility

### 6.1 Standard Protocol Support
- **IMAP4rev1**: Full compatibility with all major email clients
- **POP3**: Legacy client support
- **SMTP**: Standard email delivery
- **ManageSieve**: Sieve script management (supported by Thunderbird, etc.)

### 6.2 Feature Availability
- **LLM Features**: Server-side processing, results available via headers/flags
- **Alerts**: Administrative feature, not client-dependent
- **Undelete**: Server-side feature, accessible via JMAP or custom interfaces
- **Sieve Filtering**: Standard protocol, widely supported

### 6.3 Client-Specific Considerations
- **Headers**: LLM results can be exposed as custom headers
- **Flags**: Classification results as IMAP flags
- **Search**: Enhanced search capabilities via server-side processing
- **WebMail**: Full feature access through web interface

## 7. Implementation Recommendations

### 7.1 LLM Feature Development Strategy

#### Phase 1: Enhanced Spam Detection
- **Extend Current LLM Integration**: Build upon the existing `SpamFilterLlmConfig`
- **Multi-model Support**: Implement model ensemble for improved accuracy
- **Custom Training**: Add support for domain-specific model fine-tuning

#### Phase 2: Content Intelligence
- **Email Summarization**: Implement automatic email summary generation
- **Topic Extraction**: Add keyword and topic identification
- **Attachment Analysis**: Extend LLM analysis to document content

#### Phase 3: User Assistance
- **Smart Compose**: AI-powered email drafting assistance
- **Response Suggestions**: Context-aware reply recommendations
- **Meeting Extraction**: Automatic calendar event creation from emails

### 7.2 Technical Implementation Details

#### Configuration Example
```toml
[enterprise.ai.openai]
url = "https://api.openai.com/v1/chat/completions"
type = "chat"
model = "gpt-4"
timeout = "30s"
headers.authorization = "Bearer sk-..."

[spam-filter.llm]
enable = true
model = "openai"
temperature = 0.3
prompt = "Classify this email as SPAM, HAM, or PHISHING. Provide confidence level."
separator = "|"
categories = ["SPAM", "HAM", "PHISHING"]
confidence = ["HIGH", "MEDIUM", "LOW"]
```

#### Custom LLM Extensions
```rust
// Example extension for email summarization
pub struct EmailSummaryConfig {
    pub model: Arc<AiApiConfig>,
    pub max_length: usize,
    pub include_attachments: bool,
    pub language_detection: bool,
}

impl EmailSummaryConfig {
    pub async fn summarize_email(&self, content: &str) -> trc::Result<String> {
        let prompt = format!(
            "Summarize this email in {} words or less:\n\n{}",
            self.max_length, content
        );
        self.model.send_request(prompt, Some(0.3)).await
    }
}
```

### 7.3 Database Schema Extensions

#### LLM Analysis Results
```sql
CREATE TABLE email_llm_analysis (
    message_id BIGINT PRIMARY KEY,
    account_id INT NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    result JSON NOT NULL,
    confidence FLOAT,
    model_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_account_type (account_id, analysis_type),
    INDEX idx_created (created_at)
);
```

#### Enhanced Metrics
```sql
CREATE TABLE llm_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    model_id VARCHAR(100) NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    tokens_used INT,
    response_time_ms INT,
    success BOOLEAN,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7.4 Performance Considerations

#### Caching Strategy
- **Result Caching**: Cache LLM responses for similar content
- **Model Warming**: Keep models loaded for faster response times
- **Batch Processing**: Group similar requests for efficiency

#### Scaling Recommendations
- **Async Processing**: Use background queues for non-critical analysis
- **Rate Limiting**: Implement per-user and global rate limits
- **Fallback Mechanisms**: Graceful degradation when LLM services are unavailable

### 7.5 Security and Privacy

#### Data Protection
- **Content Sanitization**: Remove sensitive data before LLM processing
- **Encryption**: Encrypt LLM requests and responses in transit
- **Audit Logging**: Track all LLM interactions for compliance

#### Privacy Controls
- **Opt-out Mechanisms**: Allow users to disable LLM processing
- **Data Retention**: Configurable retention periods for LLM results
- **Anonymization**: Remove personally identifiable information

## 8. Advanced Feature Ideas

### 8.1 Intelligent Email Routing
- **Department Classification**: Automatically route emails to appropriate teams
- **Priority Queuing**: AI-driven priority assignment for support tickets
- **Escalation Detection**: Identify emails requiring immediate attention

### 8.2 Compliance and Governance
- **Policy Enforcement**: AI-powered policy violation detection
- **Data Loss Prevention**: Intelligent sensitive data identification
- **Retention Management**: Smart archival based on content analysis

### 8.3 User Experience Enhancements
- **Smart Search**: Natural language email search capabilities
- **Conversation Threading**: Improved thread detection using content analysis
- **Contact Intelligence**: Automatic contact information extraction and updates

## 9. Integration Patterns

### 9.1 Webhook Integration
```rust
pub struct LlmWebhookConfig {
    pub url: String,
    pub secret: String,
    pub events: Vec<LlmEvent>,
    pub retry_policy: RetryPolicy,
}

pub enum LlmEvent {
    SpamDetected,
    PhishingDetected,
    SensitiveContentFound,
    HighPriorityEmail,
}
```

### 9.2 Plugin Architecture
```rust
pub trait EmailAnalysisPlugin: Send + Sync {
    async fn analyze(&self, email: &EmailContent) -> AnalysisResult;
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    fn dependencies(&self) -> Vec<String>;
}
```

### 9.3 API Extensions
```rust
// REST API for LLM features
#[derive(Serialize, Deserialize)]
pub struct EmailAnalysisRequest {
    pub email_id: String,
    pub analysis_types: Vec<AnalysisType>,
    pub options: AnalysisOptions,
}

#[derive(Serialize, Deserialize)]
pub struct EmailAnalysisResponse {
    pub email_id: String,
    pub results: HashMap<AnalysisType, AnalysisResult>,
    pub processing_time_ms: u64,
    pub model_versions: HashMap<String, String>,
}
```

## 10. Domain and DNS Management

### 10.1 Domain Resolution Architecture

Stalwart employs a sophisticated domain resolution system that supports multiple DNS providers and caching strategies:

#### DNS Resolver Configuration
```rust
pub struct Resolvers {
    pub dns: MessageAuthenticator,
    pub dnssec: DnssecResolver,
}
```

**Supported DNS Providers:**
- **System DNS**: Uses system-configured resolvers
- **Cloudflare**: Standard and TLS-encrypted (*******)
- **Quad9**: Standard and TLS-encrypted (*******)
- **Google**: Google Public DNS (*******)
- **Custom**: User-defined DNS servers

#### Configuration Example
```toml
[resolver]
type = "cloudflare-tls"  # or "system", "quad9", "google"
timeout = "10s"
attempts = 3
concurrency = 2
```

### 10.2 Hostname and Server Identity

#### Primary Server Configuration
```rust
pub struct Network {
    pub node_id: u64,
    pub server_name: String,      // Primary hostname
    pub report_domain: String,    // Domain for reports
    pub security: Security,
    // ... other fields
}
```

**Hostname Resolution Priority:**
1. `server.hostname` configuration
2. `lookup.default.hostname` fallback
3. System hostname detection
4. Default to "localhost"

#### Multi-Domain Server Names
```toml
[server]
hostname = "mail.example.com"

[server.listener."submission"]
hostname = "submit.example.com"  # Different hostname per service
protocol = "smtp"
bind = "127.0.0.1:587"
```

### 10.3 Virtual Domain Management

#### Local Domain Detection
```rust
impl Directory {
    pub async fn is_local_domain(&self, domain: &str) -> trc::Result<bool> {
        // Checks multiple directory backends:
        // - Internal store
        // - LDAP
        // - SQL databases
        // - External SMTP/IMAP servers
        // - Memory-based lists
    }
}
```

#### Domain Configuration Methods

**1. Internal Directory (Database-backed)**
```toml
[directory."internal"]
type = "internal"
store = "rocksdb"
# Domains stored in database as Domain principals
```

**2. SQL Directory with Domain Lists**
```toml
[directory."sql"]
type = "sql"
host = "localhost"
database = "mailserver"
lookup.domains = ["example.com", "example.org", "test.net"]
```

**3. LDAP Directory**
```toml
[directory."ldap"]
type = "ldap"
host = "ldap.example.com"
lookup.domains = ["example.com", "subsidiary.com"]
```

**4. SMTP/IMAP Backend**
```toml
[directory."external"]
type = "smtp"
host = "upstream.example.com"
lookup.domains = ["hosted.example.com"]
```

### 10.4 DNS Record Generation

Stalwart can automatically generate DNS records for domains:

#### Automatic DNS Record Types
```rust
pub struct DnsRecord {
    pub typ: String,    // MX, A, AAAA, SRV, TXT, CNAME
    pub name: String,   // Record name
    pub content: String, // Record content
}
```

**Generated Records:**
- **MX Records**: Mail exchange records pointing to server
- **SRV Records**: Service discovery for SMTP, IMAP, POP3
- **DKIM Records**: Public keys for email signing
- **SPF Records**: Sender Policy Framework
- **DMARC Records**: Domain-based Message Authentication
- **MTA-STS Records**: Mail Transfer Agent Strict Transport Security

#### Example Generated Records
```dns
example.com.                MX    10 mail.example.com.
_submission._tcp.example.com. SRV   0 1 587 mail.example.com.
_imaps._tcp.example.com.     SRV   0 1 993 mail.example.com.
default._domainkey.example.com. TXT "v=DKIM1; k=rsa; p=..."
```

### 10.5 Domain-Specific Configuration

#### Per-Domain Settings
```toml
# DKIM signing per domain
[signature."domain1"]
domain = "example.com"
selector = "default"
private-key = "%{file:/path/to/key}%"

[signature."domain2"]
domain = "example.org"
selector = "mail"
private-key = "%{file:/path/to/other-key}%"
```

#### Domain-Based Routing
```rust
// RCPT TO processing checks domain locality
match directory.is_local_domain(&rcpt.domain).await {
    Ok(true) => {
        // Handle as local delivery
        match server.rcpt(directory, &rcpt.address_lcase, session_id).await {
            Ok(RcptType::Mailbox) => { /* Local mailbox */ }
            Ok(RcptType::List(members)) => { /* Mailing list */ }
            Ok(RcptType::Invalid) => { /* Reject */ }
        }
    }
    Ok(false) => {
        // Handle as relay/forward
    }
}
```

### 10.6 Multi-Tenant Domain Support

#### Tenant Isolation
```rust
pub struct AccessToken {
    pub tenant: Option<TenantInfo>,
    // ... other fields
}
```

**Features:**
- **Domain Isolation**: Each tenant can have separate domains
- **Configuration Isolation**: Per-tenant settings
- **Data Isolation**: Separate storage per tenant
- **Resource Limits**: Per-tenant quotas and limits

#### Tenant Configuration
```toml
[tenant."company1"]
domains = ["company1.com", "subsidiary1.com"]
quota = "10GB"
max-users = 100

[tenant."company2"]
domains = ["company2.org"]
quota = "5GB"
max-users = 50
```

### 10.7 Domain Validation and Security

#### Trusted Domain Lists
```rust
pub async fn is_trusted_domain(server: &Server, domain: &str, span_id: u64) -> bool {
    // Check trusted-domains lookup store
    if let Some(store) = server.core.storage.lookups.get("trusted-domains") {
        match store.key_exists(domain).await {
            Ok(true) => return true,
            // ...
        }
    }
    // Fall back to local domain check
    server.core.storage.directory.is_local_domain(domain).await
}
```

#### Domain-Based Security Policies
```toml
[list]
local-domains = ["example.org", "example.net"]
trusted-domains = ["partner.com", "affiliate.org"]
blocked-domains = ["spam.example", "malicious.net"]
```

### 10.8 DNS Caching and Performance

#### Multi-Level Caching
```rust
// DNS resolution with caching
match server.inner.cache.dns_mx.get(domain) {
    Some(cached_result) => cached_result,
    None => {
        let result = resolver.mx_lookup(domain).await?;
        server.inner.cache.dns_mx.insert_with_expiry(
            domain.to_string(),
            result.clone(),
            result.expires,
        );
        result
    }
}
```

**Cache Types:**
- **MX Record Cache**: Mail exchange lookups
- **A/AAAA Record Cache**: IP address resolution
- **DNSBL Cache**: Blacklist lookup results
- **Domain Validation Cache**: Local domain checks

### 10.9 Advanced Domain Features

#### Catch-All and Subaddressing
```rust
// Address resolution with catch-all support
let mut address = server.core.smtp.session.rcpt.subaddressing
    .to_subaddress(server, email, session_id).await;

if result.is_none() {
    if let Some(catch_all) = server.core.smtp.session.rcpt.catch_all
        .to_catch_all(server, email, session_id).await {
        address = catch_all;
    }
}
```

#### Domain Aliases and Redirects
```toml
[session.rcpt]
# Address rewriting rules
rewrite = [
    "if rcpt_domain == 'old.example.com' then 'new.example.com'",
    "if rcpt_domain == 'alias.example.com' then 'example.com'"
]
```

### 10.10 ACME/Let's Encrypt Integration

#### Automatic Certificate Management
```toml
[acme."letsencrypt"]
directory = "https://acme-v02.api.letsencrypt.org/directory"
domains = ["mail.example.com", "*.example.com"]
contact = ["mailto:<EMAIL>"]
challenge = "dns-01"  # or "http-01"
```

**Features:**
- **Wildcard Support**: DNS-01 challenge for wildcard certificates
- **Multi-Domain Certificates**: Single cert for multiple domains
- **Automatic Renewal**: Background certificate renewal
- **SNI Support**: Multiple certificates per listener

## Conclusion

Stalwart Mail Server provides an excellent foundation for building sophisticated AI-enhanced email features. The existing enterprise LLM integration demonstrates a well-architected approach that can be extended significantly. The combination of robust authentication, comprehensive spam filtering, flexible alerting, and standard protocol compliance creates numerous opportunities for innovation while maintaining broad client compatibility.

**Key strengths for LLM enhancement:**
- **Modular Architecture**: Easy to extend with new AI capabilities
- **Enterprise-Ready**: Built-in licensing and multi-tenancy support
- **Performance-Focused**: Efficient caching and async processing
- **Standards-Compliant**: Ensures compatibility across email clients
- **Security-First**: Comprehensive authentication and authorization

**Domain and DNS Management highlights:**
- **Flexible DNS Resolution**: Multiple provider support with caching
- **Virtual Domain Support**: Multi-tenant domain hosting
- **Automatic DNS Generation**: Complete DNS record management
- **Domain-Based Security**: Trusted/blocked domain lists
- **Certificate Automation**: ACME/Let's Encrypt integration

The undelete functionality, alerts system, Sieve integration, and sophisticated domain management provide additional touchpoints for AI enhancement, creating a comprehensive intelligent email platform that can scale from single-domain setups to large multi-tenant hosting environments.
