# JMAP Implementation Guide - Stalwart Mail Server Analysis

## Overview

This document provides a comprehensive analysis of how Stalwart Mail Server implements the JMAP (JSON Meta Application Protocol) specification. The implementation is split across two main crates:

- **`jmap-proto`**: Protocol definitions, parsing, and serialization
- **`jmap`**: Server-side implementation and business logic

## Architecture Overview

### High-Level Structure

```
HTTP Request → Authentication → JMAP Request Parser → Method Router → Object Handlers → Storage Layer
                                                                                      ↓
WebSocket ← State Changes ← Change Tracking ← Storage Operations ← Business Logic ← Response Builder
```

### Core Components

1. **Protocol Layer** (`jmap-proto`)
   - Request/Response parsing and serialization
   - Method definitions and routing
   - Type system and validation

2. **Implementation Layer** (`jmap`)
   - Business logic for each JMAP object type
   - Storage integration
   - Authentication and authorization
   - Real-time updates via WebSocket

3. **Storage Integration**
   - Multi-backend storage support
   - Indexing and full-text search
   - Change tracking and state management

## Protocol Implementation (`jmap-proto`)

### Request Structure

The JMAP request structure follows RFC 8620:

```rust
#[derive(Debug, Default)]
pub struct Request {
    pub using: u32,                           // Capability flags
    pub method_calls: Vec<Call<RequestMethod>>, // Array of method calls
    pub created_ids: Option<HashMap<String, AnyId>>, // ID references
}

#[derive(Debug)]
pub struct Call<T> {
    pub id: String,        // Call identifier
    pub name: MethodName,  // Method name (e.g., "Email/get")
    pub method: T,         // Method-specific arguments
}
```

### Method Types

The implementation supports all standard JMAP methods:

```rust
#[derive(Debug)]
pub enum RequestMethod {
    Get(GetRequest<get::RequestArguments>),
    Set(SetRequest<set::RequestArguments>),
    Changes(ChangesRequest),
    Copy(CopyRequest<copy::RequestArguments>),
    Query(QueryRequest<query::RequestArguments>),
    QueryChanges(QueryChangesRequest),
    // ... additional methods
}
```

### Object Types

Core JMAP objects are implemented with full property support:

- **Email**: Full RFC 8621 implementation with body parsing
- **Mailbox**: Hierarchical mailbox structure
- **Thread**: Email threading support
- **Identity**: Sender identity management
- **EmailSubmission**: Outbound email handling
- **VacationResponse**: Auto-reply functionality
- **SieveScript**: Server-side filtering

### Parsing Strategy

The implementation uses a custom JSON parser optimized for JMAP:

```rust
impl Request {
    pub fn parse(json: &[u8], max_calls: usize, max_size: usize) -> trc::Result<Self> {
        // Size validation
        if json.len() <= max_size {
            let mut parser = Parser::new(json);
            // Parse using streaming approach for memory efficiency
            // ...
        }
    }
}
```

Key features:
- **Streaming parser**: Memory-efficient for large requests
- **Property hashing**: Fast property lookup using compile-time hashes
- **Reference resolution**: Handles result references between method calls
- **Validation**: Built-in type and constraint validation

## Server Implementation (`jmap`)

### Request Handling Pipeline

```rust
impl RequestHandler for Server {
    async fn handle_jmap_request(
        &self,
        request: Request,
        access_token: Arc<AccessToken>,
        session: &HttpSessionData,
    ) -> Response {
        // 1. Create response container
        let mut response = Response::new(/*...*/);
        
        // 2. Process each method call
        for mut call in request.method_calls {
            // 3. Resolve references
            response.resolve_references(&mut call.method)?;
            
            // 4. Execute method
            let result = self.handle_method_call(/*...*/).await;
            
            // 5. Add to response
            response.push_response(call.id, call.name, result);
        }
        
        response
    }
}
```

### Object-Specific Implementations

Each JMAP object type has dedicated modules:

#### Email Implementation

```rust
impl EmailGet for Server {
    async fn email_get(
        &self,
        mut request: GetRequest<GetArguments>,
        access_token: &AccessToken,
    ) -> trc::Result<GetResponse> {
        // 1. Validate and extract IDs
        let ids = request.unwrap_ids(self.core.jmap.get_max_objects)?;
        
        // 2. Get properties to return
        let properties = request.unwrap_properties(&[/* default props */]);
        
        // 3. Check permissions and get cached data
        let cache = self.get_cached_messages(account_id).await?;
        
        // 4. Fetch email metadata and content
        for id in ids {
            // Retrieve from storage and build response object
        }
        
        Ok(response)
    }
}
```

Key features:
- **Permission checking**: ACL-based access control
- **Caching layer**: In-memory cache for frequently accessed data
- **Lazy loading**: Only fetch requested properties
- **Blob handling**: Efficient binary data management

#### Query Implementation

```rust
impl EmailQuery for Server {
    async fn email_query(
        &self,
        mut request: QueryRequest<QueryArguments>,
        access_token: &AccessToken,
    ) -> trc::Result<QueryResponse> {
        // 1. Parse filter conditions
        let mut filters = Vec::new();
        for condition in request.filter {
            match condition {
                Filter::InMailbox(mailbox_id) => {
                    filters.push(query::Filter::eq(Property::MailboxIds, mailbox_id));
                }
                Filter::Text(text) => {
                    // Full-text search integration
                    let fts_results = self.fts_filter(account_id, collection, fts_filters).await?;
                    filters.push(query::Filter::DocumentSet(fts_results));
                }
                // ... other filter types
            }
        }
        
        // 2. Execute query against storage
        let result_set = self.filter(account_id, Collection::Email, filters).await?;
        
        // 3. Apply sorting and pagination
        let (response, paginate) = self.build_query_response(&result_set, state, &request).await?;
        
        // 4. Sort results if needed
        if !request.sort.is_empty() {
            self.sort(result_set, comparators, paginate, response).await
        } else {
            Ok(response)
        }
    }
}
```

## Storage Layer Integration

### Multi-Backend Support

Stalwart supports multiple storage backends through a unified interface:

```rust
#[derive(Clone, Default)]
pub enum Store {
    SQLite(Arc<backend::sqlite::SqliteStore>),
    PostgreSQL(Arc<backend::postgres::PostgresStore>),
    MySQL(Arc<backend::mysql::MysqlStore>),
    RocksDb(Arc<backend::rocksdb::RocksDbStore>),
    FoundationDb(Arc<backend::foundationdb::FdbStore>),
    // ... other backends
}
```

### Data Storage Strategy

#### Object Storage
- **Serialization**: Uses `rkyv` for zero-copy deserialization
- **Indexing**: Multi-dimensional indexing for efficient queries
- **Versioning**: Built-in change tracking and state management

```rust
impl Store {
    async fn get_value<U>(&self, key: impl Key) -> trc::Result<Option<U>>
    where
        U: Deserialize + 'static,
    {
        // Dispatch to appropriate backend
        match self {
            Self::SQLite(store) => store.get_value(key).await,
            Self::PostgreSQL(store) => store.get_value(key).await,
            // ... other backends
        }
    }
}
```

#### Indexing Strategy

The storage layer uses a sophisticated indexing system:

1. **Property Indexes**: Fast lookup by object properties
2. **Full-Text Search**: Integrated FTS for text queries
3. **Bitmap Indexes**: Efficient set operations for filtering
4. **Change Tracking**: Maintains change logs for state synchronization

```rust
// Example indexing operation
batch
    .with_account_id(account_id)
    .with_collection(Collection::Email)
    .create_document(document_id)
    .set(Property::Subject, subject)
    .tag(Property::Keywords, keyword)
    .index(Property::MessageId, message_id)
    .commit_point();
```

### Change Tracking and State Management

#### State Representation

```rust
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum State {
    Initial,
    Exact(u64),  // Change ID
}
```

#### Change Detection

```rust
impl ChangesLookup for Server {
    async fn changes(
        &self,
        request: ChangesRequest,
        access_token: &AccessToken,
    ) -> trc::Result<ChangesResponse> {
        // 1. Map collection and validate ACLs
        let (collection, is_container) = match request.arguments {
            RequestArguments::Email => (SyncCollection::Email, false),
            RequestArguments::Mailbox => (SyncCollection::Email, true),
            // ...
        };

        // 2. Get changes since specified state
        let changelog = match &request.since_state {
            State::Initial => {
                self.store().changes(account_id, collection, Query::All).await?
            }
            State::Exact(change_id) => {
                self.store().changes(account_id, collection, Query::Since(*change_id)).await?
            }
        };

        // 3. Build response with created/updated/destroyed lists
        Ok(response)
    }
}
```

## Real-Time Updates (WebSocket)

### WebSocket Implementation

Stalwart provides real-time updates through WebSocket connections:

```rust
impl WebSocketHandler for Server {
    async fn handle_websocket_stream(
        &self,
        mut stream: WebSocketStream<TokioIo<Upgraded>>,
        access_token: Arc<AccessToken>,
        session: HttpSessionData,
    ) {
        // 1. Set up change subscription
        let mut change_types = Bitmap::new();
        let mut changes = WebSocketStateChange::new(None);

        // 2. Main event loop
        loop {
            tokio::select! {
                // Handle incoming messages
                event = stream.next() => {
                    match event {
                        Message::Text(text) => {
                            // Parse and handle JMAP requests
                            let response = self.handle_jmap_request(/*...*/).await;
                            stream.send(Message::Text(response.to_json())).await?;
                        }
                        // Handle push enable/disable
                    }
                }

                // Handle state changes
                change = self.subscribe_state_changes(access_token.primary_id()) => {
                    if change_types.contains(change.data_type) {
                        changes.changed.insert(change.account_id, change.state);
                        // Throttle and send changes
                    }
                }
            }
        }
    }
}
```

### Push Notifications

The implementation also supports HTTP push notifications:

```rust
#[derive(rkyv::Archive, rkyv::Deserialize, rkyv::Serialize)]
pub struct PushSubscription {
    pub url: String,
    pub device_client_id: String,
    pub expires: u64,
    pub verification_code: String,
    pub verified: bool,
    pub types: Bitmap<DataType>,
    pub keys: Option<Keys>,  // For Web Push encryption
}
```

## Authentication and Authorization

### Access Control

```rust
impl RequestHandler for Server {
    async fn handle_method_call(/*...*/) -> trc::Result<ResponseMethod> {
        // 1. Check method permissions
        access_token.assert_has_jmap_permission(&method)?;

        // 2. Check account access
        access_token.assert_is_member(account_id)?;

        // 3. Execute method with proper context
        match method {
            RequestMethod::Get(req) => {
                // Object-specific permission checks
                self.email_get(req, access_token).await?.into()
            }
            // ... other methods
        }
    }
}
```

### Session Management

```rust
impl SessionHandler for Server {
    async fn handle_session_resource(
        &self,
        base_url: String,
        access_token: Arc<AccessToken>,
    ) -> trc::Result<Session> {
        let mut session = Session::new(base_url, &self.core.jmap.capabilities);

        // Set primary account
        session.set_primary_account(
            access_token.primary_id().into(),
            access_token.name.to_string(),
            // ... capabilities
        );

        // Add secondary accounts with proper permissions
        for id in access_token.secondary_ids() {
            let is_readonly = self.check_readonly_access(access_token, *id).await?;
            session.add_account(*id, name, is_personal, is_readonly, capabilities);
        }

        Ok(session)
    }
}
```

## Key Design Patterns and Best Practices

### 1. Trait-Based Architecture

Stalwart uses traits extensively to provide clean abstractions:

```rust
// Core JMAP operations
pub trait JmapMethods: Sync + Send {
    fn filter(&self, account_id: u32, collection: Collection, filters: Vec<Filter>)
        -> impl Future<Output = trc::Result<ResultSet>> + Send;

    fn fts_filter<T>(&self, account_id: u32, collection: Collection, filters: Vec<FtsFilter<T>>)
        -> impl Future<Output = trc::Result<RoaringBitmap>> + Send;

    fn build_query_response<T>(&self, result_set: &ResultSet, query_state: State, request: &QueryRequest<T>)
        -> impl Future<Output = trc::Result<(QueryResponse, Option<Pagination>)>> + Send;
}

// Object-specific operations
pub trait EmailGet: Sync + Send {
    fn email_get(&self, request: GetRequest<GetArguments>, access_token: &AccessToken)
        -> impl Future<Output = trc::Result<GetResponse>> + Send;
}
```

### 2. Error Handling Strategy

Comprehensive error handling with context:

```rust
// Custom error types with tracing context
use trc::AddContext;

let result = self.core.storage.data
    .filter(account_id, collection, filters)
    .await
    .add_context(|err| {
        err.caused_by(trc::location!())
            .account_id(account_id)
            .collection(collection)
    })?;
```

### 3. Memory Management

Efficient memory usage through:
- **Zero-copy deserialization** with `rkyv`
- **Streaming JSON parsing** for large requests
- **Lazy loading** of object properties
- **Bitmap operations** for efficient set operations

### 4. Caching Strategy

Multi-level caching for performance:

```rust
// Message cache for frequently accessed data
let cache = self.get_cached_messages(account_id).await?;

// Check shared access permissions
if access_token.is_shared(account_id) {
    result_set.apply_mask(cache.shared_messages(access_token, Acl::ReadItems));
}
```

## Implementation Recommendations

### For Building Your Own JMAP Server

Based on this analysis, here are key recommendations:

#### 1. Protocol Layer Design

```rust
// Separate protocol definitions from implementation
pub mod jmap_proto {
    // Request/response types
    // Parsing and serialization
    // Validation logic
}

pub mod jmap_server {
    // Business logic
    // Storage integration
    // Authentication
}
```

#### 2. Storage Abstraction

```rust
#[async_trait]
pub trait Store: Send + Sync {
    async fn get_object(&self, account_id: u32, collection: Collection, id: u32)
        -> Result<Option<Vec<u8>>>;

    async fn query(&self, account_id: u32, collection: Collection, filters: Vec<Filter>)
        -> Result<Vec<u32>>;

    async fn write_batch(&self, operations: Vec<Operation>)
        -> Result<()>;
}
```

#### 3. Request Processing Pipeline

```rust
pub struct JmapServer {
    store: Arc<dyn Store>,
    auth: Arc<dyn Authenticator>,
    config: ServerConfig,
}

impl JmapServer {
    pub async fn handle_request(&self, request: Request, auth_token: AuthToken) -> Response {
        let mut response = Response::new();

        for call in request.method_calls {
            // 1. Validate permissions
            self.auth.check_permission(&auth_token, &call)?;

            // 2. Route to appropriate handler
            let result = match call.method {
                Method::Get(req) => self.handle_get(req, &auth_token).await,
                Method::Set(req) => self.handle_set(req, &auth_token).await,
                Method::Query(req) => self.handle_query(req, &auth_token).await,
                // ... other methods
            };

            // 3. Add to response
            response.add_result(call.id, result);
        }

        response
    }
}
```

#### 4. State Management

```rust
pub struct StateManager {
    store: Arc<dyn Store>,
}

impl StateManager {
    pub async fn get_current_state(&self, account_id: u32, collection: Collection) -> Result<State> {
        // Get latest change ID for the collection
        self.store.get_last_change_id(account_id, collection).await
    }

    pub async fn get_changes(&self, account_id: u32, collection: Collection, since_state: State)
        -> Result<ChangeLog> {
        // Return created/updated/destroyed since state
        self.store.get_changes_since(account_id, collection, since_state).await
    }
}
```

#### 5. WebSocket Integration

```rust
pub struct WebSocketManager {
    connections: Arc<RwLock<HashMap<u32, Vec<WebSocketSender>>>>,
}

impl WebSocketManager {
    pub async fn notify_changes(&self, account_id: u32, changes: StateChange) {
        let connections = self.connections.read().await;
        if let Some(senders) = connections.get(&account_id) {
            for sender in senders {
                let _ = sender.send(changes.clone()).await;
            }
        }
    }
}
```

## Performance Optimizations

### 1. Indexing Strategy

Stalwart uses multiple indexing approaches:

- **B-tree indexes** for range queries
- **Bitmap indexes** for set operations
- **Full-text indexes** for search
- **Composite indexes** for complex queries

### 2. Query Optimization

```rust
// Combine filters efficiently
let mut filters = Vec::new();

// Use bitmap operations for keyword filtering
if let Some(keywords) = request.has_keyword {
    let bitmap = self.get_keyword_bitmap(account_id, keywords).await?;
    filters.push(Filter::DocumentSet(bitmap));
}

// Use FTS for text search
if let Some(text) = request.text {
    let fts_results = self.fts_filter(account_id, collection, text_filters).await?;
    filters.push(Filter::DocumentSet(fts_results));
}

// Combine with AND operation
let result_set = self.filter(account_id, Collection::Email, filters).await?;
```

### 3. Caching Layers

Multiple caching levels:

1. **Object cache**: Frequently accessed objects
2. **Query cache**: Common query results
3. **Permission cache**: ACL evaluation results
4. **Session cache**: Authentication tokens

## Security Considerations

### 1. Input Validation

```rust
// Validate all input at protocol boundaries
impl Request {
    pub fn parse(json: &[u8], max_calls: usize, max_size: usize) -> trc::Result<Self> {
        // Size limits
        if json.len() > max_size {
            return Err(trc::LimitEvent::SizeRequest.into_err());
        }

        // Call limits
        if method_calls.len() > max_calls {
            return Err(trc::LimitEvent::CallsRequest.into_err());
        }

        // Content validation
        // ...
    }
}
```

### 2. Permission Enforcement

```rust
// Check permissions at multiple levels
impl Server {
    async fn handle_method_call(&self, method: RequestMethod, access_token: &AccessToken)
        -> trc::Result<ResponseMethod> {

        // 1. Method-level permissions
        access_token.assert_has_jmap_permission(&method)?;

        // 2. Account-level permissions
        access_token.assert_is_member(account_id)?;

        // 3. Object-level permissions (in specific handlers)
        // 4. Property-level permissions (for sensitive data)
    }
}
```

### 3. Rate Limiting

```rust
// Multiple rate limiting strategies
pub struct RateLimiter {
    // Per-user limits
    user_limits: HashMap<u32, TokenBucket>,
    // Per-IP limits
    ip_limits: HashMap<IpAddr, TokenBucket>,
    // Global limits
    global_limit: TokenBucket,
}
```

## Testing Strategy

### 1. Unit Tests

Test individual components in isolation:

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_email_get() {
        let server = test_server().await;
        let request = GetRequest::new(vec![Id::new(1)]);
        let response = server.email_get(request, &test_token()).await.unwrap();
        assert_eq!(response.list.len(), 1);
    }
}
```

### 2. Integration Tests

Test complete request/response cycles:

```rust
#[tokio::test]
async fn test_jmap_request_pipeline() {
    let server = test_server().await;
    let request = Request::parse(TEST_REQUEST_JSON, 10, 1024).unwrap();
    let response = server.handle_jmap_request(request, test_token(), &session).await;
    assert_eq!(response.method_responses.len(), 1);
}
```

### 3. Compliance Tests

Ensure JMAP RFC compliance:

```rust
// Test against official JMAP test suite
#[tokio::test]
async fn test_rfc_compliance() {
    // Run official JMAP conformance tests
}
```

## Conclusion

Stalwart's JMAP implementation demonstrates several key architectural principles:

1. **Clean separation** between protocol and implementation
2. **Trait-based design** for extensibility
3. **Comprehensive error handling** with context
4. **Multi-backend storage** support
5. **Real-time updates** via WebSocket
6. **Performance optimization** through caching and indexing
7. **Security-first** approach with multiple validation layers

This architecture provides a solid foundation for building scalable, secure, and compliant JMAP servers. The modular design allows for easy testing, maintenance, and extension while maintaining high performance and reliability.

For your own implementation, consider adopting similar patterns while adapting them to your specific requirements and constraints. The trait-based architecture is particularly valuable for creating testable and maintainable code.

